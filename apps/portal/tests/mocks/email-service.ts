import config from '@/config';
import prisma from '@/lib/prisma';
import { ExtendedWarrantyRequest } from '@/types/warranty';
import crypto from 'crypto';

export const mockEmailService = {
    emails: [] as any[],
    reset() {
        this.emails = [];
    },

    async getEmail(emailId: string) {
        return this.emails.find((email) => email.id === emailId);
    },

    async logEmail(html: string, emailData: any) {
        const digest = crypto.createHash('sha256').update(html).digest('hex');

        await prisma.emailOutbox.create({
            data: {
                to_email: emailData.to,
                from_email: config.email.from,
                subject: emailData.subject,
                html: html,
                text: emailData.text || '',
                digest,
                status: 'sent',
                response: 'Mock email sent successfully',
                params: JSON.stringify({ type: emailData.emailType }),
                send_date: new Date(),
            },
        });
    },
    async send(emailData: {
        to: string;
        from?: string;
        subject: string;
        react: React.ReactNode;
        text?: string;
    }) {
        if (!emailData.from) {
            console.log('No from email provided, using default from email');
            emailData.from = config.email.from;
        }
        this.emails.push(emailData);
        await this.logEmail('<mock-html>', emailData);
        return { success: true, result: emailData };
    },
    getLastEmail() {
        return this.emails[this.emails.length - 1];
    },
    getAllEmails() {
        return this.emails;
    },
    async sendWarrantyRequestEmail(warrantyRequest: ExtendedWarrantyRequest) {
        const emailData = {
            to: warrantyRequest.email,
            subject: 'Your Warranty Request',
            emailType: 'warranty_request',
        };
        this.emails.push(emailData);
        await this.logEmail('<mock-warranty-request-email>', emailData);
        return { success: true };
    },

    async sendCustomerAuthorizationApprovedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        approvedHours?: number,
        updateNotes?: string
    ) {
        const emailData = {
            to: warrantyRequest.email,
            subject: 'Your Warranty Authorization Has Been Approved',
            emailType: 'warranty_authorization_approved',
        };
        this.emails.push(emailData);
        await this.logEmail('<mock-warranty-authorization-approved-email>', emailData);
        return { success: true };
    },

    async sendPasswordResetEmail(
        email: string,
        token: string,
        isMobile: boolean
    ): Promise<{ success: boolean; error?: any }> {
        const emailData = {
            to: email,
            subject: 'Password Reset Request',
            emailType: 'password_reset',
        };
        this.emails.push(emailData);
        await this.logEmail('<mock-password-reset-email>', emailData);
        return { success: true };
    },

    async sendTechnicianAuthorizationApprovedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        approvedHours?: number,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        const emailData = {
            to: warrantyRequest.listing.email,
            subject: 'Your Warranty Authorization Has Been Approved',
            emailType: 'technician_warranty_authorization_approved',
        };
        this.emails.push(emailData);
        await this.logEmail('<mock-technician-warranty-authorization-approved-email>', emailData);
        return { success: true };
    },

    async sendTechnicianAuthorizationFeedbackRequestedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        const emailData = {
            to: warrantyRequest.listing.email,
            subject: 'Feedback Request For Your Warranty Authorization',
            emailType: 'technician_warranty_authorization_feedback_requested',
        };
        this.emails.push(emailData);
        await this.logEmail('<mock-technician-warranty-authorization-feedback-requested-email>', emailData);
        return { success: true };
    },

    async sendTechnicianAuthorizationRejectedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        const emailData = {
            to: warrantyRequest.listing.email,
            subject: 'Your Warranty Authorization Has Been Rejected',
            emailType: 'technician_warranty_authorization_rejected',
        };
        this.emails.push(emailData);
        await this.logEmail('<mock-technician-warranty-authorization-rejected-email>', emailData);
        return { success: true };
    }
};
