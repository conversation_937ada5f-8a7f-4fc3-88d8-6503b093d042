import { createHandler } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { emailService, slackService } from '@/lib/services';
import { WarrantyRequestService } from '@/lib/services/warranty-request.service';
import type { ExtendedWarrantyRequest } from '@/types/warranty';
import type { NextRequest } from 'next/server';
import { z } from 'zod';

export const GET = createHandler(
    async function (req: NextRequest) {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        if (!this.user?.company_id) {
            return Response.json({ error: 'User not associated with a company' }, { status: 400 });
        }

        // Get search params from the request URL
        const searchParams = req.nextUrl.searchParams;

        const filters = {
            page: parseInt(searchParams.get('page') || '1'),
            pageSize: parseInt(searchParams.get('pageSize') || '10'),
            representativeId: searchParams.get('representativeId'),
            status: searchParams.get('status'),
            component: searchParams.get('component'),
            search: searchParams.get('search'),
            rvVin: searchParams.get('rvVin'),
            rvModel: searchParams.get('rvModel'),
        };

        const result = await WarrantyRequestService.getWarrantyRequests(
            this.user.company_id,
            this.user.id,
            filters
        );

        return Response.json(result);
    },
    {
        requiredRole: "OEM"
    }
);

// Match the expanded schema from the form
const postWarrantyRequestSchema = z.object({
    // Service Request Details
    id: z.string().min(1, 'ID is required'),
    complaint: z.string().min(10, 'Please provide a detailed description of the issue'),
    cause: z.string().optional(),
    correction: z.string().optional(),
    component_id: z.string().min(1, 'Please select a component').optional().nullable(),
    notes_to_provider: z.string().optional().nullable(),
    requires_return: z.boolean().optional(),

    // Customer Information
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    email: z.string().email('Valid email is required'),
    phone: z.string().min(10, 'Valid phone number is required'),
    contact_preference: z.enum(['sms', 'phone']).optional(),

    // Location Information
    location: z
        .object({
            address: z.string().min(1, 'Address is required'),
            longitude: z.number().optional(),
            latitude: z.number().optional(),
        })
        .optional(),

    attachments: z
        .array(
            z.object({
                id: z.string().optional().nullable(),
                title: z.string(),
                type: z.string(),
                url: z.string(),
                required: z.boolean(),
                component_id: z.string().optional().nullable(),
                completed: z.boolean().optional(),
            })
        )
        .optional(),

    // RV Information
    rv_vin: z
        .string()
        .min(17, 'VIN must be at least 17 characters')
        .max(17, 'VIN cannot exceed 17 characters'),
    rv_make: z.string().min(1, 'Make is required'),
    rv_model: z.string().min(1, 'Model is required'),
    rv_year: z.string().min(1, 'Year is required'),
    rv_type: z.string().min(1, 'Type is required'),

    // Additional Information
    approved_hours: z.number().min(0).optional(),
});

export const POST = createHandler(
    async function () {
        if (!this.user?.company_id) {
            return Response.json({ error: 'User not associated with a company' }, { status: 400 });
        }

        // Get company info for the request
        const company = await prisma.company.findUnique({
            where: { id: this.user.company_id },
            select: {
                id: true,
                name: true,
            },
        });

        try {
            // Create the warranty request using the service
            const request = await WarrantyRequestService.createWarrantyRequest(
                this.validatedData,
                this.user.company_id,
                this.user.id,
                company?.name
            );

            // Handle email and notifications if not already sent
            if (!request.email_sent_at && request.status === 'REQUEST_APPROVED') {
                const updatedWarrantyRequest = await prisma.warrantyRequest.findUnique({
                    where: { id: request.id },
                    include: {
                        company: true,
                        component: true,
                    },
                });

                await emailService.sendWarrantyRequestEmail(
                    updatedWarrantyRequest as ExtendedWarrantyRequest
                );

                await prisma.warrantyRequest.update({
                    where: { id: updatedWarrantyRequest.id },
                    data: {
                        email_sent_at: new Date(),
                    },
                });

                // Send Slack notification for new warranty request
                try {
                    await slackService.notifyNewWarrantyRequest(
                        updatedWarrantyRequest,
                        updatedWarrantyRequest.company?.name || 'Unknown Company'
                    );
                } catch (slackError) {
                    console.error('Failed to send Slack notification for warranty request:', slackError);
                    // Don't fail the entire request if Slack notification fails
                }
            }

            return Response.json(request);
        } catch (error) {
            console.error('Error creating warranty request:', error);
            return Response.json({ error: 'Failed to create warranty request' }, { status: 500 });
        }
    },
    {
        validateBody: postWarrantyRequestSchema,
        requiredRole: "OEM",
        requireAuth: true
    }
);
