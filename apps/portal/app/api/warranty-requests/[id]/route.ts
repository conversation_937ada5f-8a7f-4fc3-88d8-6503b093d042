import { createHandler } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { emailService } from '@/lib/services';
import type { ExtendedWarrantyRequest, WarrantyAttachment } from '@/types/warranty'; // Import the extended type
import type { TimelineEventType, WarrantyRequestStatus } from '@rvhelp/database'; // Import enum
import { z } from 'zod';

// Match the expanded schema from the form
const putWarrantyRequestSchema = z.object({
    // Service Request Details
    complaint: z.string().min(10, 'Please provide a detailed description of the issue'),
    cause: z.string().optional(),
    correction: z.string().optional(),
    component_id: z.string().optional(),
    notes_to_provider: z.string().optional().nullable(),
    status: z
        .enum([
            'REQUEST_CREATED',
            'REQUEST_APPROVED',
            'REQUEST_REJECTED',
            'JOB_REQUESTED',
            'JOB_ACCEPTED',
            'JOB_STARTED',
            'JOB_COMPLETED',
            'JOB_CANCELLED',
            'AUTHORIZATION_REQUESTED',
            'AUTHORIZATION_APPROVED',
            'AUTHORIZATION_REJECTED',
            'AUTHORIZATION_FEEDBACK',
            'PARTS_ORDERED',
            'INVOICE_CREATED',
            'INVOICE_PAID',
        ])
        .optional(), // Optional status field

    // Customer Information
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    email: z.string().email('Valid email is required'),
    phone: z.string().min(10, 'Valid phone number is required'),
    requires_return: z.boolean().optional(),
    // contact_preference: z.enum(['sms', 'phone']).optional(),

    // Location Information
    location: z
        .object({
            address: z.string().optional(),
            latitude: z.number().optional(),
            longitude: z.number().optional(),
        })
        .optional(),

    attachments: z
        .array(
            z.object({
                id: z.string().optional().nullable(),
                title: z.string(),
                type: z.string(),
                url: z.string(),
                required: z.boolean(),
                component_id: z.string().optional().nullable(),
                completed: z.boolean().optional(),
            })
        )
        .optional()
        .nullable(),

    // RV Information
    rv_vin: z
        .string()
        .min(17, 'VIN must be at least 17 characters')
        .max(17, 'VIN cannot exceed 17 characters'),
    rv_make: z.string().min(1, 'Make is required'),
    rv_model: z.string().min(1, 'Model is required'),
    rv_year: z.string().min(1, 'Year is required'),
    rv_type: z.string().min(1, 'Type is required'),

    // Additional Information
    approved_hours: z.number().min(0).optional(),
    authorization_type: z.enum(['SPECIFIC', 'GENERAL']).optional(),
    event_type: z
        .enum([
            'PREAUTHORIZATION_APPROVED',
            'PREAUTHORIZATION_REJECTED',
            'AUTHORIZATION_APPROVED',
            'AUTHORIZATION_REJECTED',
            'AUTHORIZATION_FEEDBACK',
            'PARTS_ORDERED',
            'INVOICE_PAID',
        ])
        .optional(),
    update_notes: z.string().optional(), // Optional field for update notes
    admin_notes: z.string().optional().nullable(),
});

export const PUT = createHandler(
    async function () {
        if (!this?.user?.email || !this?.user?.company_id) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from URL: /api/warranty-requests/[id]
        const urlParts = this.req.url.split('/');
        const id = urlParts[urlParts.length - 1];

        const { event_type, update_notes, component_id, attachments, ...otherFields } =
            this.validatedData;

        let status = otherFields.status;
        if (event_type) {
            switch (event_type) {
                case 'PREAUTHORIZATION_APPROVED':
                    status = 'REQUEST_APPROVED';
                    break;
                case 'PREAUTHORIZATION_REJECTED':
                    status = 'REQUEST_REJECTED';
                    break;
                case 'AUTHORIZATION_APPROVED':
                    status = 'AUTHORIZATION_APPROVED';
                    break;
                case 'AUTHORIZATION_REJECTED':
                    status = 'AUTHORIZATION_REJECTED';
                    break;
                case 'AUTHORIZATION_FEEDBACK':
                    status = 'AUTHORIZATION_FEEDBACK';
                    break;
                case 'PARTS_ORDERED':
                    status = 'PARTS_ORDERED';
                    break;
                case 'INVOICE_PAID':
                    status = 'INVOICE_PAID';
                    break;
            }
        }

        try {
            const existing = (await prisma.warrantyRequest.findFirst({
                where: { id, company_id: this.user.company_id },
            })) as ExtendedWarrantyRequest;
            if (!existing) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            // remove all attachments that have the old component_id, except completed forms
            let newAttachments: WarrantyAttachment[] = undefined;
            if (attachments && existing.component_id !== component_id) {
                newAttachments = [];
                for (const attachment of attachments) {
                    if (
                        attachment.component_id !== component_id &&
                        attachment.completed === false
                    ) {
                        continue;
                    } else {
                        newAttachments.push(attachment);
                    }
                }
            }

            const updatedWarrantyRequest = await prisma.warrantyRequest.update({
                where: { id: existing.id, company_id: this.user.company_id },
                data: {
                    ...otherFields,
                    ...(status && { status: status as WarrantyRequestStatus }), // Cast status to enum
                    ...(component_id && {
                        component: {
                            connect: { id: component_id },
                        },
                    }),
                    attachments: newAttachments,
                },
                include: {
                    company: true,
                    component: true,
                    listing: true,
                },
            }) as ;

            // Create a WarrantyRequestUpdate record if status was updated
            if (status && event_type) {
                await prisma.timelineUpdate.create({
                    data: {
                        job_id: existing.job_id,
                        warranty_request_id: existing.id,
                        updated_by_id: this.user.id, // ID of the user making the update
                        event_type: event_type as TimelineEventType, // The new status
                        details: update_notes ? { notes: update_notes } : undefined, // Store notes if provided
                        date: new Date(),
                    },
                });
            }

            // if the Warranty Request is just moving to REQUEST_APPROVED status, create a user, job and send email
            if (
                !updatedWarrantyRequest.email_sent_at &&
                existing.status !== 'REQUEST_APPROVED' &&
                status === 'REQUEST_APPROVED'
            ) {
                await emailService.sendWarrantyRequestEmail(
                    updatedWarrantyRequest as ExtendedWarrantyRequest
                );

                await prisma.warrantyRequest.update({
                    where: { id: updatedWarrantyRequest.id },
                    data: {
                        email_sent_at: new Date(), // Set the email_sent_at field
                    },
                });
            }

            // Send authorization approval email
            if (
                (
                    existing.status !== 'AUTHORIZATION_APPROVED' &&
                    status === 'AUTHORIZATION_APPROVED'
                ) || (
                    existing.status !== 'PARTS_ORDERED' &&
                    status === 'PARTS_ORDERED'
                )
            ) {
                await emailService.sendWarrantyAuthorizationApprovedEmail(
                    updatedWarrantyRequest as ExtendedWarrantyRequest,
                    otherFields.approved_hours,
                    update_notes
                );
                await emailService.sendTechnicianAuthorizationApprovedEmail(
                    updatedWarrantyRequest as ExtendedWarrantyRequest,
                    otherFields.approved_hours,
                    update_notes
                );
            }

            if (
                existing.status !== 'AUTHORIZATION_FEEDBACK' &&
                status === 'AUTHORIZATION_FEEDBACK'
            ) {
                // await emailService.sendWarrantyAuthorizationFeedbackRequestedEmail(
                //     updatedWarrantyRequest as ExtendedWarrantyRequest,
                //     update_notes
                // );
                await emailService.sendTechnicianAuthorizationFeedbackRequestedEmail(
                    updatedWarrantyRequest as ExtendedWarrantyRequest,
                    update_notes
                );
            }

            // Send authorization rejection email
            if (
                existing.status !== 'AUTHORIZATION_REJECTED' &&
                status === 'AUTHORIZATION_REJECTED'
            ) {
                await emailService.sendTechnicianAuthorizationRejectedEmail(
                    updatedWarrantyRequest as ExtendedWarrantyRequest,
                    update_notes
                );
            }

            return Response.json(updatedWarrantyRequest);
        } catch (error) {
            console.error('Error updating warranty request:', error);
            return Response.json({ error: 'Failed to update warranty request' }, { status: 500 });
        }
    },
    {
        validateBody: putWarrantyRequestSchema,
        requiredRole: "OEM"
    }
);

export const GET = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await prisma.user.findFirst({
            where: { email: this.user.email },
        });

        if (!user?.company_id) {
            return Response.json({ error: 'User not associated with a company' }, { status: 400 });
        }

        // Extract ID from URL: /api/warranty-requests/[id]
        const urlParts = this.req.url.split('/');
        const uuid = urlParts[urlParts.length - 1];

        try {
            const request = await prisma.warrantyRequest.findFirst({
                where: { uuid: uuid, company_id: user.company_id },
                include: {
                    timeline_updates: {
                        orderBy: { date: 'asc' },
                        include: {
                            updated_by: {
                                select: { first_name: true, last_name: true },
                            },
                        },
                    },
                    oem_user: {
                        select: { first_name: true, last_name: true, email: true },
                    },
                    listing: {
                        select: {
                            first_name: true,
                            last_name: true,
                            email: true,
                            phone: true,
                            business_name: true,
                            pricing_settings: true,
                        },
                    },
                },
            });

            if (!request) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            return Response.json(request);
        } catch (error) {
            console.error('Error fetching warranty request:', error);
            return Response.json({ error: 'Failed to fetch warranty request' }, { status: 500 });
        }
    },
    { requiredRole: "OEM" }
);
