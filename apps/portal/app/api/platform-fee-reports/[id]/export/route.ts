import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { endOfDay, startOfDay } from "date-fns";

export const GET = createHandler(
    async function (request: Request, { params }: { params: { id: string } }) {
        try {
            console.log("🔧 [Platform Fee Reports Export API] Exporting report:", params.id);

            // Parse the report ID (format: startTime-endTime)
            const [startTime, endTime] = params.id.split("-").map(Number);
            const periodStart = new Date(startTime);
            const periodEnd = new Date(endTime);

            console.log(`🔧 [Platform Fee Reports Export API] Period: ${periodStart.toISOString()} to ${periodEnd.toISOString()}`);

            // Fetch provider invoices for this period
            const providerInvoices = await prisma.invoice.findMany({
                where: {
                    status: "PAID",
                    paid_at: {
                        gte: startOfDay(periodStart),
                        lte: endOfDay(periodEnd)
                    }
                },
                include: {
                    provider: {
                        select: {
                            business_name: true
                        }
                    },
                    warranty_provider_request: {
                        select: {
                            rv_vin: true
                        }
                    }
                },
                orderBy: {
                    paid_at: "desc"
                }
            });

            console.log(`🔧 [Platform Fee Reports Export API] Found ${providerInvoices.length} invoices to export`);

            // Generate CSV content
            const headers = [
                "Invoice Number",
                "Provider",
                "RV VIN",
                "Invoice Amount",
                "Quantity",
                "Unit Price",
                "Date Paid"
            ];

            // Add provider invoice rows
            const providerRows = providerInvoices.map(invoice => [
                invoice.invoice_number,
                invoice.provider?.business_name || "Unknown Provider",
                invoice.warranty_provider_request?.rv_vin || "Unknown VIN",
                (invoice.amount / 100).toFixed(2), // Convert from cents to dollars
                "1", // Quantity for provider invoice
                (invoice.amount / 100).toFixed(2), // Unit price same as total for provider invoice
                invoice.paid_at?.toLocaleDateString() || "Unknown Date"
            ]);

            // Add single platform fee line item with quantity
            const totalPlatformFees = providerInvoices.length * 50;
            const platformFeeRow = [
                "PLATFORM-FEES",
                "RV Help Platform Fee",
                `${providerInvoices.length} Warranty Requests`,
                totalPlatformFees.toFixed(2), // Total amount
                providerInvoices.length.toString(), // Quantity = number of warranty requests
                "50.00", // Unit price = $50 per request
                new Date().toLocaleDateString() // Current date for platform fee
            ];

            // Combine all rows
            const rows = [...providerRows, platformFeeRow];

            const csvContent = [
                headers.join(","),
                ...rows.map(row => row.map(field => `"${field}"`).join(","))
            ].join("\n");

            console.log(`🔧 [Platform Fee Reports Export API] Generated CSV with ${rows.length} rows`);

            return new Response(csvContent, {
                headers: {
                    "Content-Type": "text/csv",
                    "Content-Disposition": `attachment; filename="platform-fee-report-${periodStart.toISOString().split('T')[0]}-to-${periodEnd.toISOString().split('T')[0]}.csv"`
                }
            });
        } catch (error) {
            console.error("Error exporting platform fee report:", error);
            return this.respond({
                success: false,
                error: error instanceof Error ? error.message : "Failed to export platform fee report"
            }, 500);
        }
    },
    {
        requiredRole: "OEM"
    }
);
