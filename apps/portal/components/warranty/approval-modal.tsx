import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import type { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { Check, ClipboardList, FileText, Package, X } from "lucide-react";
import { useMemo, useState } from "react";
import { AttachmentSection } from "./warranty-request-card/sections/attachment-section";

interface ApprovalModalProps {
	open: boolean;
	onClose: () => void;
	request: ExtendedWarrantyRequest;
	onStatusUpdated: () => void;
	company: ExtendedCompany;
}

export function ApprovalModal({
	open,
	onClose,
	request,
	onStatusUpdated,
	company
}: ApprovalModalProps) {
	const [updateNotes, setUpdateNotes] = useState("");
	const [approvedHours, setApprovedHours] = useState<string>(
		request.approved_hours?.toString() || ""
	);
	const [partsOrdered, setPartsOrdered] = useState(false);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const hourlyRate = useMemo(() => {
		return (
			request.listing?.pricing_settings.warranty_rate ||
			request.listing?.pricing_settings.hourly_rate ||
			0
		);
	}, [request]);

	const requestMessage = useMemo(() => {
		if (!request.timeline_updates || request.timeline_updates.length === 0) {
			return null;
		}

		// Find the most recent timeline update of type AUTHORIZATION_REQUESTED
		const authorizationUpdates = request.timeline_updates
			.filter((update) => update.event_type === "AUTHORIZATION_REQUESTED")
			.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

		return authorizationUpdates.length > 0 ? authorizationUpdates[0] : null;
	}, [request]);

	const returnDetailsUpdate = useMemo(() => {
		if (!request.timeline_updates || request.timeline_updates.length === 0) {
			return null;
		}

		// Find the latest TECHNICIAN_UPDATED event with "Return details updated" message
		const returnUpdates = request.timeline_updates
			.filter(
				(update) =>
					update.event_type === "TECHNICIAN_UPDATED" &&
					update.details?.notes === "Return details updated"
			)
			.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

		return returnUpdates.length > 0 ? returnUpdates[0] : null;
	}, [request]);

	async function handleSubmit(
		eventType:
			| "AUTHORIZATION_APPROVED"
			| "AUTHORIZATION_REJECTED"
			| "AUTHORIZATION_FEEDBACK"
			| "PARTS_ORDERED"
	) {
		setLoading(true);
		setError(null);

		// Validate that notes are required for rejection
		if (
			(eventType === "AUTHORIZATION_REJECTED" ||
				eventType === "AUTHORIZATION_FEEDBACK") &&
			!updateNotes.trim()
		) {
			setError("Notes are required when rejecting authorization");
			setLoading(false);
			return;
		}

		if (
			(eventType === "AUTHORIZATION_APPROVED" ||
				eventType === "PARTS_ORDERED") &&
			request.estimated_hours > parseFloat(approvedHours || "0")
		) {
			setError(
				"Approved hours must be greater than or equal to estimated hours to Approve. To approve less than estimate, use Request Feedback and technican can resubmit estimate."
			);
			setLoading(false);
			return;
		}

		try {
			const res = await fetch(`/api/warranty-requests/${request.id}`, {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					...request,
					event_type: eventType,
					update_notes: updateNotes,
					approved_hours: parseFloat(approvedHours)
				})
			});

			if (!res.ok) {
				const data = await res.json();
				throw new Error(data.error || "Failed to update status");
			}

			onClose();
			onStatusUpdated();
			setUpdateNotes("");
			setPartsOrdered(false);
		} catch (err: any) {
			setError(err.message || "Unknown error");
		} finally {
			setLoading(false);
		}
	}

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-3xl max-h-[85vh] p-0">
				<DialogHeader className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
					<div className="flex items-center gap-3">
						<div className="rounded-full p-2 bg-blue-50">
							<svg
								className="w-5 h-5 text-blue-600"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fillRule="evenodd"
									d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
									clipRule="evenodd"
								/>
							</svg>
						</div>
						<div>
							<DialogTitle className="text-lg font-semibold text-gray-900">
								Authorization Review
							</DialogTitle>
							<p className="text-sm text-gray-500">
								Review and update warranty authorization
							</p>
						</div>
					</div>
				</DialogHeader>

				<div className="flex-1 overflow-y-auto">
					<div className="p-6">
						<Tabs defaultValue="details" className="w-full">
							<TabsList
								className={`grid w-full mb-6 h-10 ${request.requires_return ? "grid-cols-3" : "grid-cols-2"}`}
							>
								<TabsTrigger
									value="details"
									className="flex items-center gap-2 text-sm"
								>
									<ClipboardList className="h-4 w-4" />
									Details
								</TabsTrigger>
								<TabsTrigger
									value="attachments"
									className="flex items-center gap-2 text-sm"
								>
									<FileText className="h-4 w-4" />
									Attachments
								</TabsTrigger>
								{request.requires_return && (
									<TabsTrigger
										value="return-details"
										className="flex items-center gap-2 text-sm"
									>
										<Package className="h-4 w-4" />
										Return Details
									</TabsTrigger>
								)}
							</TabsList>

							<TabsContent value="details" className="space-y-5">
								{/* Compact Complaint Details */}
								<div className="space-y-4">
									{/* Top Row: Complaint, Cause, Correction */}
									<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
										<div>
											<h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
												<span className="w-2 h-2 bg-blue-500 rounded-full"></span>
												Complaint
											</h3>
											<div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 border border-gray-200 min-h-[60px]">
												{request.complaint}
											</div>
										</div>

										<div>
											<h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
												<span className="w-2 h-2 bg-orange-500 rounded-full"></span>
												Cause
											</h3>
											<div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 border border-gray-200 min-h-[60px]">
												{request.cause || (
													<span className="text-gray-400 italic">
														Not specified
													</span>
												)}
											</div>
										</div>

										<div>
											<h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
												<span className="w-2 h-2 bg-green-500 rounded-full"></span>
												Correction
											</h3>
											<div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 border border-gray-200 min-h-[60px]">
												{request.correction || (
													<span className="text-gray-400 italic">
														Not specified
													</span>
												)}
											</div>
										</div>
									</div>

									{/* Second Row: Technician Notes */}
									{requestMessage && (
										<div>
											<h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
												<span className="w-2 h-2 bg-purple-500 rounded-full"></span>
												Technician Notes
											</h3>
											<div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 border border-gray-200 min-h-[60px]">
												{requestMessage.details?.notes || (
													<span className="text-gray-400 italic">No notes</span>
												)}
											</div>
										</div>
									)}

									{/* Third Row: Contact Information */}
									{request.listing && (
										<div>
											<h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
												<span className="w-2 h-2 bg-indigo-500 rounded-full"></span>
												Technician Contact Information
											</h3>
											<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
												<div>
													<h3 className="block text-xs font-medium text-gray-600 mb-1">
														Name
													</h3>
													<div className="text-sm text-gray-700">
														{request.listing.first_name &&
															request.listing.last_name ? (
															`${request.listing.first_name} ${request.listing.last_name}`
														) : request.listing.business_name ? (
															request.listing.business_name
														) : (
															<span className="text-gray-400 italic">
																Not provided
															</span>
														)}
													</div>
												</div>
												<div>
													<h3 className="block text-xs font-medium text-gray-600 mb-1">
														Phone
													</h3>
													<div className="text-sm text-gray-700">
														{request.listing.phone || (
															<span className="text-gray-400 italic">
																Not provided
															</span>
														)}
													</div>
												</div>
												<div>
													<h3 className="block text-xs font-medium text-gray-600 mb-1">
														Email
													</h3>
													<div className="text-sm text-gray-700">
														{request.listing.email || (
															<span className="text-gray-400 italic">
																Not provided
															</span>
														)}
													</div>
												</div>
											</div>

											{/* Pricing Information Row */}
											<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
												<div>
													<h3 className="block text-xs font-medium text-gray-600 mb-1">
														Hourly Rate
													</h3>
													<div className="text-sm font-medium text-gray-700">
														${hourlyRate?.toFixed(2) || "0.00"}/hr
													</div>
												</div>
												<div>
													<h3 className="block text-xs font-medium text-gray-600 mb-1">
														Service Call Fee
													</h3>
													<div className="text-sm font-medium text-gray-700">
														$
														{request.listing.pricing_settings.dispatch_fee?.toFixed(
															2
														) || "0.00"}
													</div>
												</div>
												<div></div>
											</div>
										</div>
									)}
								</div>

								{/* Divider */}
								<div className="border-t border-gray-200 my-4"></div>

								{/* Hours Section */}
								<div className="grid grid-cols-2 gap-4">
									<div>
										<label
											htmlFor="estimated-hours"
											className="block text-xs font-medium text-gray-700 mb-2"
										>
											Estimated Hours
										</label>
										<Input
											id="estimated-hours"
											value={request.estimated_hours?.toString() || ""}
											disabled
											className="bg-gray-50 border-gray-200"
										/>
									</div>
									<div>
										<label
											htmlFor="approved-hours"
											className="block text-xs font-medium text-gray-700 mb-2"
										>
											Approved Hours
										</label>
										<Input
											id="approved-hours"
											type="number"
											step="0.5"
											min="0"
											value={approvedHours}
											onChange={(e) => setApprovedHours(e.target.value)}
											placeholder="Enter hours"
											className="border-gray-200 focus:border-blue-500"
										/>
									</div>
								</div>

								{/* Notes Section */}
								<div>
									<div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-4">
										<p className="text-sm text-amber-800 font-medium">
											⚠️ Note: The authorization notes will be visible to both the
											customer and technician.
										</p>
									</div>
									<h3 className="block text-xs font-medium text-gray-700 mb-2">
										Authorization Notes
									</h3>
									<Textarea
										value={updateNotes}
										onChange={(e) => setUpdateNotes(e.target.value)}
										placeholder="Add notes for this authorization update..."
										rows={2}
										className="border-gray-200 focus:border-blue-500 resize-none"
									/>
									<p className="text-xs text-gray-500 mt-1 pb-2">
										Required for rejection and feedback, optional for approval
									</p>

								</div>

								{error && (
									<div className="bg-red-50 border border-red-200 rounded-lg p-3">
										<div className="text-red-600 text-sm flex items-center gap-2">
											<X className="h-4 w-4" />
											{error}
										</div>
									</div>
								)}
							</TabsContent>

							<TabsContent value="attachments" className="space-y-4">
								<AttachmentSection request={request} />
							</TabsContent>

							{request.requires_return && (
								<TabsContent value="return-details" className="space-y-4">
									<div className="space-y-4">
										{returnDetailsUpdate ? (
											<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
												<h3 className="text-sm font-medium text-blue-700 mb-3 flex items-center gap-2">
													<Package className="h-4 w-4" />
													Return Details
												</h3>
												<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
													<div>
														<h3 className="block text-xs font-medium text-blue-600 mb-1">
															Height
														</h3>
														<div className="bg-white p-3 rounded border border-blue-200 text-sm font-medium text-blue-700">
															{returnDetailsUpdate.details?.return_details
																?.height || 0}{" "}
															inches
														</div>
													</div>
													<div>
														<h3 className="block text-xs font-medium text-blue-600 mb-1">
															Width
														</h3>
														<div className="bg-white p-3 rounded border border-blue-200 text-sm font-medium text-blue-700">
															{returnDetailsUpdate.details?.return_details
																?.width || 0}{" "}
															inches
														</div>
													</div>
													<div>
														<h3 className="block text-xs font-medium text-blue-600 mb-1">
															Depth
														</h3>
														<div className="bg-white p-3 rounded border border-blue-200 text-sm font-medium text-blue-700">
															{returnDetailsUpdate.details?.return_details
																?.depth || 0}{" "}
															inches
														</div>
													</div>
													<div>
														<h3 className="block text-xs font-medium text-blue-600 mb-1">
															Weight
														</h3>
														<div className="bg-white p-3 rounded border border-blue-200 text-sm font-medium text-blue-700">
															{returnDetailsUpdate.details?.return_details
																?.weight || 0}{" "}
															lbs
														</div>
													</div>
												</div>
												<div className="mt-3 text-xs text-blue-600">
													Updated on{" "}
													{new Date(
														returnDetailsUpdate.date
													).toLocaleDateString()}{" "}
													at{" "}
													{new Date(
														returnDetailsUpdate.date
													).toLocaleTimeString()}
												</div>
											</div>
										) : (
											<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
												<div className="text-yellow-700 text-sm flex items-center gap-2">
													<Package className="h-4 w-4" />
													Return details not yet provided
												</div>
												<p className="text-xs text-yellow-600 mt-1">
													The technician has not yet provided the return details
													for this item.
												</p>
											</div>
										)}
									</div>
								</TabsContent>
							)}
						</Tabs>
					</div>
				</div>

				{/* Parts Ordered Checkbox */}
				<div className="px-6 py-3 border-t border-gray-100 bg-gray-50/50">
					<div className="flex items-center space-x-2">
						<Checkbox
							id="parts-ordered"
							checked={partsOrdered}
							onCheckedChange={(checked) => setPartsOrdered(checked as boolean)}
						/>
						<h3
							htmlFor="parts-ordered"
							className="text-sm font-medium text-gray-700 cursor-pointer"
						>
							Technician has ordered parts
						</h3>
					</div>
				</div>

				<DialogFooter className="px-6 py-4 border-t border-gray-100 bg-gray-50/50">
					<div className="flex justify-between items-center w-full">
						<Button
							variant="outline"
							onClick={onClose}
							disabled={loading}
							className="border-gray-300 text-gray-700 hover:bg-gray-50"
						>
							Cancel
						</Button>

						<div className="flex gap-3">
							<Button
								variant="outline"
								onClick={() => handleSubmit("AUTHORIZATION_REJECTED")}
								disabled={loading}
								className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
							>
								<div className="flex items-center gap-2">
									<X className="h-4 w-4" />
									Reject
								</div>
							</Button>
							<Button
								variant="outline"
								onClick={() => handleSubmit("AUTHORIZATION_FEEDBACK")}
								disabled={loading}
								className="border-amber-300 text-amber-700 hover:bg-amber-50 hover:border-amber-400"
							>
								<div className="flex items-center gap-2">
									<X className="h-4 w-4" />
									Request Feedback
								</div>
							</Button>
							<Button
								onClick={() =>
									handleSubmit(
										partsOrdered ? "PARTS_ORDERED" : "AUTHORIZATION_APPROVED"
									)
								}
								disabled={loading}
								style={{ backgroundColor: company.brand_color }}
								className="text-white hover:opacity-90 shadow-sm"
							>
								<div className="flex items-center gap-2">
									<Check className="h-4 w-4" />
									{partsOrdered ? "Approve & Mark Parts Ordered" : "Approve"}
								</div>
							</Button>
						</div>
					</div>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
