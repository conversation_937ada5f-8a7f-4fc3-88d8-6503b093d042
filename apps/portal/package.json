{"name": "@rvhelp/oem-portal", "version": "0.1.0", "private": true, "scripts": {"clean": "rm -rf node_modules /next", "dev": "next dev --port 4000", "build": "next build", "build-with-generate": "pnpm --filter @rvhelp/services build && prisma generate && next build", "prebuild": "pnpm --filter @rvhelp/database build && pnpm --filter @rvhelp/services build", "start": "next start", "check-types": "tsc --noEmit", "lint": "next lint", "lint:strict": "next lint && tsc --noEmit", "db:generate": "pnpm --filter @rvhelp/database db:generate", "test": "jest"}, "prisma": {"seed": "node ../../packages/database/prisma/seed.ts", "schema": "../../packages/database/prisma/schema.prisma"}, "dependencies": {"@auth/prisma-adapter": "2.8.0", "@rvhelp/database": "workspace:*", "@rvhelp/services": "workspace:*", "@headlessui/react": "^2.1.10", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.1", "@next/third-parties": "^15.1.4", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "6.13.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@react-email/components": "^0.0.25", "@react-pdf/renderer": "^4.3.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "bcryptjs": "^2.4.3", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "heroicons": "^2.2.0", "jsonwebtoken": "^9.0.2", "next": "^14.2.25", "next-auth": "^4.24.10", "pdf-lib": "^1.17.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.26.0", "@playwright/test": "^1.48.1", "@rvhelp/eslint-config": "1.0.0", "@rvhelp/typescript-config": "1.0.0", "@swc/core": "^1.9.3", "@swc/jest": "^0.2.37", "@testing-library/jest-dom": "^6.6.3", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.12", "@types/mapbox-gl": "^3.4.1", "@types/node": "^22.7.7", "@types/nodemailer": "^6.4.16", "@types/qrcode.react": "^1.0.5", "@types/react": "18.3.11", "@types/react-dom": "^18.3.1", "@types/react-slick": "^0.23.13", "@types/stripe": "^8.0.416", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "babel-jest": "^29.7.0", "csv-parse": "^5.5.6", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-next": "14.2.25", "eslint-plugin-react": "^7.37.1", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8", "prisma": "6.13.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.6.3"}, "optionalDependencies": {"@next/swc-darwin-arm64": "latest", "@next/swc-darwin-x64": "latest", "@next/swc-linux-arm64-gnu": "latest", "@next/swc-linux-arm64-musl": "latest", "@next/swc-linux-x64-gnu": "latest", "@next/swc-linux-x64-musl": "latest", "@next/swc-win32-arm64-msvc": "latest", "@next/swc-win32-ia32-msvc": "latest", "@next/swc-win32-x64-msvc": "latest"}}