/**
 * Web app services - extending shared services with app-specific methods
 */

import config from "@/config";
import prisma from "@/lib/prisma";
import { createSlackService, InvoiceService } from "@rvhelp/services";
import { getEmailService } from "./services/email.service";

// Configure web-specific email service
export const emailService = getEmailService();

export const slackService = createSlackService(
	prisma,
	{
		providerGrowth: "*********************************************************************************",
		dailyStats: "*********************************************************************************",
		josiahMann: "*********************************************************************************",
		providerLeads: "*********************************************************************************",
		memberSignups: "*********************************************************************************",
		oemJobs: "*********************************************************************************",
	},
	process.env.NODE_ENV === "test"
);

export const invoiceService = new InvoiceService(
	prisma, {
	webAppUrl: config.appUrl,
	portalAppUrl: config.isDevelopment ? 'http://localhost:4000' : process.env.NEXT_PUBLIC_PORTAL_APP_URL || 'http://oem.rvhelp.com',
}
);


// Export types for convenience
export type { CreateInvoiceInput, CreatePlatformInvoiceInput } from "@rvhelp/services";
