import { ExtendedWarrantyRequest } from "@/types/warranty";
import {
	Job, Listing, Location, PrismaClient, User as PrismaUser, Quote,
	Review,
	TimelineUpdate
} from "@rvhelp/database";

declare global {
	var prisma: PrismaClient | undefined;

	interface Window {
		dataLayer: any[];
		gtag: (...args: any[]) => void;
		Intercom?: (command: string, ...args: any[]) => void;
	}
}

export type ListingWithOwner = Listing & {
	owner?: User | null; // Include the owner of the listing
};

export type ListingWithLocation = Listing & {
	locations: Location[];
	location?: Location | null;
	owner?: User | null;
	vacation_mode?: {
		enabled: boolean;
		message?: string;
	};
}

export type RejectionReason =
	| "too_busy"
	| "schedule_conflict"
	| "outside_travel_area"
	| "not_a_good_fit"
	| "other";

export interface RankingFactors {
	score: number;
	distance_score: number;
	certification_score: number;
	seniority_score: number;
	reviews_score: number;
	probability?: number;
	daily_seed?: number;
}

export type PricingSettings = {
	dispatch_fee: number;
	hourly_rate: number;
	warranty_rate: number;
	charges_travel_fee: boolean;
	travel_fee: number;
	charges_parts_fee: boolean;
};

export type ListingWithPricingSettings = Listing & {
	pricing_settings: PricingSettings;
};

export type ListingWithLocation = Listing & {
	location: Location;
	ranking_factors?: RankingFactors;
	pricing_settings: PricingSettings;
	distance?: number;
};

// listing with reviews
type ListingWithReviews = Listing & {
	reviews: Review[];
	users: UserListing[];
	vacation_mode?: {
		enabled: boolean;
		message?: string;
	};
};

type ListingWithReviewsAndLocation = ListingWithReviews & {
	locations: Location[];
	location?: Location | null;
	owner?: User | null;
};

interface Global {
	TextDecoder: typeof TextDecoder;
	TextEncoder: typeof TextEncoder;
}

// user with rv details
type User = PrismaUser & {
	rv_details: {
		type: string;
		year: number;
		make: string;
		model: string;
	};
	customer_warranty_requests: ExtendedWarrantyRequest[];
	technician_warranty_requests: ExtendedWarrantyRequest[];
};


export type QuoteWithJob = Quote & {
	job: Job & {
		user: {
			first_name: string;
			last_name: string;
			email: string;
			phone: string;
		};
		warranty_request?: ExtendedWarrantyRequest;
	};
};

export interface QuoteWithListing extends Quote {
	listing: ListingWithLocation;
	messages: QuoteMessage[];
	unread_messages_count?: number;
	unread_message?: Message;
	messages_count?: number;
	includes_first_hour?: boolean;
	preferred_date?: Date;
	preferred_time_slot?: string;
	estimated_hours?: number;
}

export interface JobWithUserAndLocation extends Job {
	accepted_quote?: QuoteWithListing;
	user: User & {
		rating: number;
		num_reviews: number;
	};
	location: {
		address: string;
		city: string;
		state: string;
		latitude: number;
		longitude: number;
	};
	warranty_request?: ExtendedWarrantyRequest;
	quotes?: QuoteWithListing[];
	timeline_updates?: TimelineUpdate[];
	// RV Details - adding rv_vin to match database schema
	rv_vin?: string;
}

type SchedulingTimeframe =
	| "within_24_hours"
	| "within_48_hours"
	| "within_a_week"
	| "within_2_weeks"
	| "more_than_2_weeks"
	| "need_more_info";

// Define the message type based on actual API response structure
export type MessageSender = {
	id: string;
	type: "PROVIDER" | "USER";
	name: string;
	email: string;
	avatar?: string | null;
};

export type ApiQuoteMessage = {
	id: string;
	quote_id: string;
	content: string;
	status: string;
	type: string;
	attachments: any[];
	metadata: any;
	created_at: string;
	sent_at: string | null;
	delivered_at: string | null;
	read_at: string | null;
	sender: MessageSender;
	recipient: MessageSender;
};

export type TroubleshootingRequestWithUser = TroubleshootingRequest & {
	user: User;
	listing: Listing;
};

// Create a unified lead type
export type UnifiedLead = {
	id: string;
	type: "quote" | "troubleshooting";
	created_at: string;
	status: string;
	customer: {
		first_name: string;
		last_name: string;
		email: string;
		phone: string | null;
	};
	location: any;
	message: string;
	category?: string;
	// Original data for modal handling
	originalData: QuoteWithJob | TroubleshootingRequestWithUser;
};
