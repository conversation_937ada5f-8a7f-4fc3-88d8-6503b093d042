import "@testing-library/jest-dom";
import { TextDecoder, TextEncoder } from "util";
import { prisma as mockPrisma } from "./mocks/prisma-mock";
import "./utils/api-test-utils";

global.TextEncoder = TextEncoder as any;
global.TextDecoder = TextDecoder as any;

// Set up test environment variables
process.env.STRIPE_CONNECT_WEBHOOK_SECRET = "whsec_test_secret";

// Mock Prisma globally
jest.mock("@prisma/client", () => ({
	prisma: mockPrisma,
	JobStatus: {
		OPEN: "OPEN",
		ASSIGNED: "ASSIGNED",
		COMPLETED: "COMPLETED",
		CANCELLED: "CANCELLED"
	},
	QuoteStatus: {
		PENDING: "PENDING",
		ACCEPTED: "ACCEPTED",
		REJECTED: "REJECTED",
		CUSTOMER_REJECTED: "CUSTOMER_REJECTED",
		IN_PROGRESS: "IN_PROGRESS",
		WITHDRAWN: "WITHDRAWN",
		EXPIRED: "EXPIRED",
		COMPLETED: "COMPLETED"
	},
	QuoteMessageType: {
		TEXT: "TEXT",
		IMAGE: "IMAGE",
		FILE: "FILE"
	},
	QuoteMessageParticipantType: {
		USER: "USER",
		PROVIDER: "PROVIDER"
	},
	RVHelpVerificationLevel: {
		NONE: "NONE",
		PROFILE_COMPLETE: "PROFILE_COMPLETE",
		VERIFIED: "VERIFIED",
		CERTIFIED_PRO: "CERTIFIED_PRO"
	},
	InvoiceStatus: {
		DRAFT: "DRAFT",
		SENT: "SENT",
		PAID: "PAID",
		OVERDUE: "OVERDUE",
		CANCELLED: "CANCELLED"
	},
	TimelineEventType: {
		REQUEST_SUBMITTED: "REQUEST_SUBMITTED",
		REQUEST_APPROVED: "REQUEST_APPROVED",
		AUTHORIZATION_REQUESTED: "AUTHORIZATION_REQUESTED",
		AUTHORIZATION_APPROVED: "AUTHORIZATION_APPROVED",
		AUTHORIZATION_FEEDBACK: "AUTHORIZATION_FEEDBACK",
		PARTS_ORDERED: "PARTS_ORDERED",
		AUTHORIZATION_DENIED: "AUTHORIZATION_DENIED",
		INVOICE_CREATED: "INVOICE_CREATED",
		INVOICE_PAID: "INVOICE_PAID",
		WORK_COMPLETED: "WORK_COMPLETED",
		REQUEST_CANCELLED: "REQUEST_CANCELLED",
		UPDATE_POSTED: "UPDATE_POSTED"
	},
	__esModule: true,
	default: mockPrisma
}));

// Mock the prisma instance file
jest.mock("@/lib/prisma", () => ({
	__esModule: true,
	prisma: mockPrisma,
	default: mockPrisma
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
	useRouter: () => ({
		push: jest.fn(),
		replace: jest.fn(),
		prefetch: jest.fn()
	}),
	useSearchParams: () => ({
		get: jest.fn()
	})
}));

// Mock BlacklistService globally
jest.mock("@/lib/services/blacklist.service", () => ({
	BlacklistService: {
		checkEmailAccess: jest.fn().mockResolvedValue({ isBlacklisted: false }),
		checkUserAccess: jest.fn().mockResolvedValue({ isBlacklisted: false })
	}
}));

// Mock Timeline service globally
jest.mock("@/lib/services/timeline.service", () => ({
	timelineService: {
		createTimelineUpdate: jest.fn().mockResolvedValue({})
	}
}));

// Mock Email service globally - now using shared services
jest.mock("@/lib/services", () => ({
	emailService: {
		send: jest.fn().mockResolvedValue({}),
		batchSend: jest.fn().mockResolvedValue({ results: [], errors: [] }),
		sendMembershipWelcomeEmail: jest.fn().mockResolvedValue({ success: true }),
		sendPasswordResetEmail: jest.fn().mockResolvedValue({ success: true }),
		sendVerificationEmail: jest.fn().mockResolvedValue({ success: true }),
		sendVerificationCompletedEmail: jest.fn().mockResolvedValue({ success: true }),
		sendWelcomeEmail: jest.fn().mockResolvedValue({ success: true }),
		sendWarrantyAuthorizationRequestedEmail: jest.fn().mockResolvedValue({ success: true }),
		sendServiceRequestPasswordSetupEmail: jest.fn().mockResolvedValue({ success: true })
	},
	slackService: {
		sendToJosiahMann: jest.fn().mockResolvedValue(undefined),
		sendToProviderLeads: jest.fn().mockResolvedValue(undefined),
		notifyNewMemberSignup: jest.fn().mockResolvedValue(undefined),
		notifyProviderVerified: jest.fn().mockResolvedValue(undefined),
		notifyPlatformInvoiceGenerated: jest.fn().mockResolvedValue(undefined),
		notifyPlatformInvoiceError: jest.fn().mockResolvedValue(undefined),
		notifyProviderGrowth: jest.fn().mockResolvedValue(undefined),
		notifyDailyStats: jest.fn().mockResolvedValue(undefined),
		notifyProviderLeads: jest.fn().mockResolvedValue(undefined),
		notifyMemberSignups: jest.fn().mockResolvedValue(undefined),
		notifyOemJobs: jest.fn().mockResolvedValue(undefined),
		notifyJosiahMann: jest.fn().mockResolvedValue(undefined)

	},
	invoiceService: {
		createInvoice: jest.fn().mockResolvedValue({}),
		createPlatformInvoice: jest.fn().mockResolvedValue({}),
		getInvoiceById: jest.fn().mockResolvedValue({}),
		getProviderInvoiceByWarrantyRequestId: jest.fn().mockResolvedValue({}),
		updateInvoiceStatus: jest.fn().mockResolvedValue({}),
		updateInvoice: jest.fn().mockResolvedValue({}),
		updateInvoiceItem: jest.fn().mockResolvedValue({}),
		deleteInvoice: jest.fn().mockResolvedValue({}),
		generatePlatformInvoice: jest.fn().mockResolvedValue({}),
		getPendingPlatformInvoices: jest.fn().mockResolvedValue([]),
		getInvoicesByProvider: jest.fn().mockResolvedValue([])
	}
}));

// Mock SMS service globally
jest.mock("@/lib/services/sms.service", () => ({
	smsService: {
		send: jest.fn().mockResolvedValue({}),
		client: {
			messages: {
				list: jest.fn().mockResolvedValue([])
			}
		}
	}
}));

// Slack service is now mocked through @/lib/services above

// Mock Queue service globally
jest.mock("@/lib/queue/qstash", () => ({
	queueMessage: jest.fn().mockResolvedValue({ success: true })
}));

// Mock config globally
jest.mock("@/config", () => ({
	__esModule: true,
	default: {
		appUrl: "http://localhost:3000",
		assetsUrl: "http://localhost:3000",
		apps: {
			appStore: { id: "test", url: "test" },
			googlePlay: { id: "test", url: "test" }
		},
		activeCampaign: { apiKey: "test" },
		aws: {
			accessKeyId: "test",
			bucket: "test",
			region: "test",
			secretAccessKey: "test"
		},
		cronSecret: "test-secret",
		email: {
			allowedDomains: ["test.com"],
			allowedEmails: ["<EMAIL>"],
			from: "<EMAIL>",
			fromName: "Test"
		},
		firstPromoter: { apiKey: "test", companyId: "test" },
		google: { apiKey: "test", iosApiKey: "test" },
		intercom: {
			accessToken: "test",
			appId: "test",
			bugTicketTypeId: "test",
			featureTicketTypeId: "test"
		},
		isDevelopment: true,
		isProMembershipAvailable: false,
		MAPBOX_PUBLIC_TOKEN: "test",
		mysql: {
			host: "test",
			user: "test",
			password: "test",
			database: "test"
		},
		posthog: { apiKey: "test", apiHost: "test" },
		qstash: {
			currentSigningKey: "test",
			nextSigningKey: "test",
			token: "test"
		},
		redis: { url: "test" },
		RESEND_API_KEY: "test",
		rvsg: {
			username: "test",
			password: "test",
			webhookApiKey: "test"
		},
		server: "test",
		stripe: {
			publishableKey: "test",
			secretKey: "test",
			membership: {
				standard: { id: "test", priceId: "test" },
				premium: { id: "test", priceId: "test" }
			}
		},
		twilio: {
			accountSid: "test",
			authToken: "test",
			fromNumber: "test",
			allowedNumbers: ["test"]
		}
	}
}));
