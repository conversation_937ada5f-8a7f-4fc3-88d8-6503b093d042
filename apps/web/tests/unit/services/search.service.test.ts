import { SearchService } from "@/lib/services/search.service";
import { RVHelpVerificationLevel } from "@rvhelp/database";
import { mockPrisma } from "../../mocks/prisma-mock";

describe("SearchService", () => {
	const baseParams = {
		lat: "35.0",
		lng: "-85.0",
		filters: {}
	};
	beforeEach(() => {
		jest.clearAllMocks();
		(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([]);
		(mockPrisma.listing.count as jest.Mock).mockResolvedValue(0);
		(mockPrisma.location.findMany as jest.Mock).mockResolvedValue([]);
	});

	describe("listing sorting", () => {
		it("should sort verified listings by distance first, then non-verified by distance", async () => {
			const mockListings = [
				{
					id: "far_verified",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 40,
					location: {
						latitude: 35.2,
						longitude: -85.2,
						radius: 50
					}
				},
				{
					id: "close_non_verified",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					distance: 10,
					location: {
						latitude: 35.1,
						longitude: -85.1,
						radius: 50
					}
				},
				{
					id: "close_verified",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 20,
					location: {
						latitude: 35.3,
						longitude: -85.3,
						radius: 50
					}
				},
				{
					id: "far_non_verified",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					distance: 30,
					location: {
						latitude: 35.4,
						longitude: -85.4,
						radius: 50
					}
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
			(mockPrisma.listing.count as jest.Mock).mockResolvedValue(
				mockListings.length
			);

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-repair"
				},
				1,
				10
			);

			// Verified listings should come first, sorted by distance
			expect(result.listings.map((l) => l.id)).toEqual([
				"close_verified", // Verified, 20 miles
				"far_verified", // Verified, 40 miles
				"close_non_verified", // Non-verified, 10 miles
				"far_non_verified" // Non-verified, 30 miles
			]);
		});

		it("should respect service radius when filtering listings", async () => {
			const mockListings = [
				{
					id: "verified_within_radius",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 40,
					location: {
						latitude: 35.1,
						longitude: -85.1,
						radius: 50
					}
				},
				{
					id: "verified_outside_radius",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 60,
					location: {
						latitude: 35.2,
						longitude: -85.2,
						radius: 50
					}
				},
				{
					id: "non_verified_within_radius",
					rv_help_verification_level: null,
					distance: 30,
					location: {
						latitude: 35.3,
						longitude: -85.3,
						radius: 50
					}
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
			(mockPrisma.listing.count as jest.Mock).mockResolvedValue(
				mockListings.length
			);

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-inspection"
				},
				1,
				10
			);

			// Should only include listings within their service radius
			expect(result.listings.map((l) => l.id)).toEqual([
				"verified_within_radius",
				"non_verified_within_radius"
			]);
			expect(result.listings.length).toBe(2);
		});

		it("should handle inspection radius for rv-inspection category", async () => {
			const mockListings = [
				{
					id: "within_inspection_radius",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 60,
					location: {
						latitude: 35.1,
						longitude: -85.1,
						radius: 50,
						inspection_radius: 70
					}
				},
				{
					id: "outside_inspection_radius",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 65,
					location: {
						latitude: 35.2,
						longitude: -85.2,
						radius: 70,
						inspection_radius: 50
					}
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
			(mockPrisma.listing.count as jest.Mock).mockResolvedValue(
				mockListings.length
			);

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-inspection"
				},
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				"within_inspection_radius"
			]);
			expect(result.listings.length).toBe(1);
		});

		it("should prioritize verification level before certification levels for rv-inspection", async () => {
			// this order should be verified_master_medium, verified_certified_far, non_verified_master_close, non_verified_certified_veryclose
			const mockListings = [
				{
					id: "non_verified_master_close",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					nrvia_inspector_level: 3, // Master
					distance: 20,
					location: {
						latitude: 35.1,
						longitude: -85.1,
						radius: 100,
						inspection_radius: 100
					}
				},
				{
					id: "verified_certified_far",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					nrvia_inspector_level: 2, // Certified
					distance: 90,
					location: {
						latitude: 35.2,
						longitude: -85.2,
						radius: 100,
						inspection_radius: 100
					}
				},
				{
					id: "verified_master_medium",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					nrvia_inspector_level: 3, // Master
					distance: 50,
					location: {
						latitude: 35.3,
						longitude: -85.3,
						radius: 100,
						inspection_radius: 100
					}
				},
				{
					id: "non_verified_certified_veryclose",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					nrvia_inspector_level: 2, // Certified
					distance: 10,
					location: {
						latitude: 35.4,
						longitude: -85.4,
						radius: 100,
						inspection_radius: 100
					}
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
			(mockPrisma.listing.count as jest.Mock).mockResolvedValue(
				mockListings.length
			);

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-inspection"
				},
				1,
				10
			);

			// Verified listings should come first (masters, then others randomly)
			// Then non-verified listings (masters, then others randomly)
			const verifiedListings = result.listings.slice(0, 2);
			const nonVerifiedListings = result.listings.slice(2);

			expect(verifiedListings.map((l) => l.id)).toContain(
				"verified_master_medium"
			);
			expect(verifiedListings.map((l) => l.id)).toContain(
				"verified_certified_far"
			);
			expect(nonVerifiedListings.map((l) => l.id)).toContain(
				"non_verified_master_close"
			);
			expect(nonVerifiedListings.map((l) => l.id)).toContain(
				"non_verified_certified_veryclose"
			);
		});

		it("should prioritize verification level for rv-repair category", async () => {
			const mockListings = [
				{
					id: "non_verified_master_20",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 3, // Master
					distance: 20,
					location: {
						latitude: 35.1,
						longitude: -85.1,
						radius: 100
					}
				},
				{
					id: "verified_registered_90",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1, // Registered
					distance: 90,
					location: {
						latitude: 35.2,
						longitude: -85.2,
						radius: 100
					}
				},
				{
					id: "verified_master_100",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 3, // Master
					distance: 100,
					location: {
						latitude: 35.3,
						longitude: -85.3,
						radius: 100
					}
				},
				{
					id: "verified_certified_50",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 2, // Certified
					distance: 50,
					location: {
						latitude: 35.4,
						longitude: -85.4,
						radius: 100
					}
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-repair"
				},
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				"verified_certified_50", // Verified + Within 50
				"verified_registered_90", // Verified + Beyond 50
				"verified_master_100", // Verified + Beyond 50
				"non_verified_master_20" // Non-verified
			]);
		});

		it("should sort listings by review quality when both have sufficient reviews", async () => {
			const mockListings = [
				{
					id: "high_rated_many_reviews",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rating: 4.9,
					num_reviews: 25,
					distance: 40,
					location: {
						latitude: 35.1,
						longitude: -85.1,
						radius: 100
					}
				},
				{
					id: "high_rated_fewer_reviews",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rating: 4.8,
					num_reviews: 15,
					distance: 20,
					location: {
						latitude: 35.2,
						longitude: -85.2,
						radius: 100
					}
				},
				{
					id: "low_rated_many_reviews",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rating: 4.5,
					num_reviews: 30,
					distance: 10,
					location: {
						latitude: 35.3,
						longitude: -85.3,
						radius: 100
					}
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
			(mockPrisma.listing.count as jest.Mock).mockResolvedValue(
				mockListings.length
			);

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-inspection"
				},
				1,
				10
			);

			// High rated (≥4.8) should come first, with more reviews (>20) winning
			expect(result.listings.map((l) => l.id)).toEqual([
				"high_rated_many_reviews", // 4.9 rating, 25 reviews
				"high_rated_fewer_reviews", // 4.8 rating, 15 reviews
				"low_rated_many_reviews" // 4.5 rating, 30 reviews
			]);
		});
	});

	describe("rv-repair sorting", () => {
		it("should sort verified techs by certification level and distance within 50 miles", async () => {
			const mockListings = [
				{
					id: "verified_master_40",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 3,
					distance: 40,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "verified_registered_30",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1,
					distance: 30,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				},
				{
					id: "verified_certified_20",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 2,
					distance: 20,
					location: { latitude: 35.3, longitude: -85.3, radius: 100 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				"verified_master_40",
				"verified_certified_20",
				"verified_registered_30"
			]);
		});

		it("should sort non-verified techs after verified techs", async () => {
			const mockListings = [
				{
					id: "non_verified_master_10",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 3,
					distance: 10,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "verified_registered_90",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1,
					distance: 90,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				"verified_registered_90",
				"non_verified_master_10"
			]);
		});
	});

	describe("pagination", () => {
		it("should correctly paginate after filtering by service radius", async () => {
			// Create 30 listings, but only 20 within service radius
			const mockListings = Array.from({ length: 30 }, (_, i) => ({
				id: `listing_${i + 1}`,
				rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
				rating: 4.0,
				num_reviews: 5,
				short_description: "Description",
				distance: 20 + i,
				location: {
					latitude: 35.1,
					longitude: -85.1,
					radius: i < 20 ? 100 : 30 // First 20 within radius, last 10 outside
				}
			}));

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
			(mockPrisma.listing.count as jest.Mock).mockResolvedValue(
				mockListings.length
			);

			// Get first page (10 items)
			const page1 = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				1,
				10
			);

			// Get second page (10 items)
			const page2 = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				2,
				10
			);

			// Get third page (should have no items since only 20 total are within radius)
			const page3 = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				3,
				10
			);

			// First page should have first 10 listings
			expect(page1.listings.length).toBe(10);
			expect(page1.listings[0].id).toBe("listing_1");
			expect(page1.listings[9].id).toBe("listing_10");

			// Second page should have next 10 listings
			expect(page2.listings.length).toBe(10);
			expect(page2.listings[0].id).toBe("listing_11");
			expect(page2.listings[9].id).toBe("listing_20");

			// Third page should be empty since we only have 20 valid listings
			expect(page3.listings.length).toBe(0);

			// Total count should be consistent across all pages
			expect(page1.total).toBe(20);
			expect(page2.total).toBe(20);
			expect(page3.total).toBe(20);
		});
	});

	describe("rv-inspection sorting", () => {
		it("should sort inspectors by verification, distance bands, certification, and reviews", async () => {
			const mockListings = [
				// 0-100 mile band
				{
					id: "verified_level3_4.9_50reviews_20miles",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					nrvia_inspector_level: 3,
					rating: 4.9,
					num_reviews: 50,
					distance: 20,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "verified_level2_4.7_100reviews_40miles",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					nrvia_inspector_level: 2,
					rating: 4.7,
					num_reviews: 100,
					distance: 40,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				},
				// 100-200 mile band
				{
					id: "verified_level3_4.8_20reviews_120miles",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					nrvia_inspector_level: 3,
					rating: 4.8,
					num_reviews: 20,
					distance: 120,
					location: { latitude: 35.3, longitude: -85.3, radius: 200 }
				},
				// Non-verified listings
				{
					id: "nonverified_level3_4.9_80reviews_10miles",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					nrvia_inspector_level: 3,
					rating: 4.9,
					num_reviews: 80,
					distance: 10,
					location: { latitude: 35.4, longitude: -85.4, radius: 100 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-inspection" },
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				// Verified 0-100 mile band
				"verified_level3_4.9_50reviews_20miles", // Level 3, 4.9 rating wins over level 2
				"verified_level2_4.7_100reviews_40miles", // Level 2
				// Verified 100-200 mile band
				"verified_level3_4.8_20reviews_120miles", // Different distance band
				// Non-verified (always last regardless of rating/reviews)
				"nonverified_level3_4.9_80reviews_10miles"
			]);
		});
	});

	describe("rv-repair sorting with distance bands", () => {
		it("should sort verified techs by certification level within each 50-mile band", async () => {
			const mockListings = [
				// 0-50 mile band
				{
					id: "verified_master_40",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 3,
					distance: 40,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "verified_registered_20",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1,
					distance: 20,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				},
				// 50-100 mile band
				{
					id: "verified_master_90",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 3,
					distance: 90,
					location: { latitude: 35.3, longitude: -85.3, radius: 100 }
				},
				{
					id: "verified_certified_60",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 2,
					distance: 60,
					location: { latitude: 35.4, longitude: -85.4, radius: 100 }
				},
				// 100-150 mile band
				{
					id: "verified_master_120",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 3,
					distance: 120,
					location: { latitude: 35.5, longitude: -85.5, radius: 150 }
				},
				{
					id: "verified_registered_110",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1,
					distance: 110,
					location: { latitude: 35.6, longitude: -85.6, radius: 150 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				// 0-50 mile band, sorted by certification
				"verified_master_40",
				"verified_registered_20",
				// 50-100 mile band, sorted by certification
				"verified_master_90",
				"verified_certified_60",
				// 100-150 mile band, sorted by certification
				"verified_master_120",
				"verified_registered_110"
			]);
		});

		it("should sort non-verified techs by certification level within each 50-mile band", async () => {
			const mockListings = [
				// 0-50 mile band
				{
					id: "non_verified_master_40",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 3,
					distance: 40,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "non_verified_certified_20",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 2,
					distance: 20,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				},
				// 50-100 mile band
				{
					id: "non_verified_master_90",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 3,
					distance: 90,
					location: { latitude: 35.3, longitude: -85.3, radius: 100 }
				},
				{
					id: "non_verified_registered_60",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 1,
					distance: 60,
					location: { latitude: 35.4, longitude: -85.4, radius: 100 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				// 0-50 mile band, sorted by certification
				"non_verified_master_40",
				"non_verified_certified_20",
				// 50-100 mile band, sorted by certification
				"non_verified_master_90",
				"non_verified_registered_60"
			]);
		});

		it("should maintain verification level priority across all distance bands", async () => {
			const mockListings = [
				// Non-verified in first band
				{
					id: "non_verified_master_20",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					rvtaa_technician_level: 3,
					distance: 20,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				// Verified in second band
				{
					id: "verified_registered_60",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1,
					distance: 60,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);

			const result = await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-repair" },
				1,
				10
			);

			expect(result.listings.map((l) => l.id)).toEqual([
				"verified_registered_60", // Verified comes first despite being further
				"non_verified_master_20" // Non-verified comes last despite being closer and higher certified
			]);
		});
	});

	describe("certification filtering", () => {
		it("should construct correct WHERE clause to filter out falsy certification levels for rv-inspection", async () => {
			const mockListings = [
				{
					id: "valid_inspector",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					nrvia_inspector_level: 3,
					distance: 40,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				}
			];

			let capturedWhere;
			(mockPrisma.listing.findMany as jest.Mock).mockImplementation((args) => {
				capturedWhere = args.where;
				return Promise.resolve(mockListings);
			});

			await SearchService.getListingsByLatLong(
				{ ...baseParams, category: "rv-inspection" },
				1,
				10
			);

			// Test that the WHERE clause includes the correct certification level filter
			expect(capturedWhere.AND[0].nrvia_inspector_level).toEqual({ gt: 0 });
		});

		it("should respect specific certification level filters when provided", async () => {
			const mockListings = [
				{
					id: "level_3_tech",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 3,
					distance: 20,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "level_2_tech",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 2,
					distance: 10,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				},
				{
					id: "level_1_tech",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					rvtaa_technician_level: 1,
					distance: 15,
					location: { latitude: 35.3, longitude: -85.3, radius: 100 }
				}
			];

			(mockPrisma.listing.findMany as jest.Mock).mockImplementation(({ where }) => {
				const filtered = mockListings.filter((listing) => {
					if (where.AND[0].rvtaa_technician_level?.in) {
						return where.AND[0].rvtaa_technician_level.in.includes(
							listing.rvtaa_technician_level
						);
					}
					return true;
				});
				return Promise.resolve(filtered);
			});

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-repair",
					filters: {
						certificationLevels: [2, 3] // Only want level 2 and 3 techs
					}
				},
				1,
				10
			);

			expect(result.listings.length).toBe(2);
			expect(result.listings.map((l) => l.id)).toEqual(
				expect.arrayContaining(["level_3_tech", "level_2_tech"])
			);
			expect(result.listings.map((l) => l.id)).not.toContain("level_1_tech");
		});
	});

	describe("verification filtering", () => {
		it("should filter to only verified providers when verified=true", async () => {
			const mockListings = [
				{
					id: "verified_provider",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 20,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "certified_pro_provider",
					rv_help_verification_level: RVHelpVerificationLevel.CERTIFIED_PRO,
					distance: 30,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				},
				{
					id: "profile_complete_provider",
					rv_help_verification_level: RVHelpVerificationLevel.PROFILE_COMPLETE,
					distance: 40,
					location: { latitude: 35.3, longitude: -85.3, radius: 100 }
				},
				{
					id: "unverified_provider",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					distance: 10,
					location: { latitude: 35.4, longitude: -85.4, radius: 100 }
				}
			];

			let capturedWhere;
			(mockPrisma.listing.findMany as jest.Mock).mockImplementation((args) => {
				capturedWhere = args.where;
				// Filter to simulate database behavior
				const filtered = mockListings.filter(
					(listing) =>
						listing.rv_help_verification_level !== RVHelpVerificationLevel.NONE
				);
				return Promise.resolve(filtered);
			});

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-repair",
					filters: {
						verified: true
					}
				},
				1,
				10
			);

			// Should exclude unverified providers (NONE level)
			expect(result.listings.length).toBe(3);
			expect(result.listings.map((l) => l.id)).toContain("verified_provider");
			expect(result.listings.map((l) => l.id)).toContain(
				"certified_pro_provider"
			);
			expect(result.listings.map((l) => l.id)).toContain(
				"profile_complete_provider"
			);
			expect(result.listings.map((l) => l.id)).not.toContain(
				"unverified_provider"
			);

			// Check that the WHERE clause has the correct verification filter within the AND clause
			expect(capturedWhere.AND[0]).toMatchObject({
				rv_help_verification_level: {
					not: RVHelpVerificationLevel.NONE
				}
			});
		});

		it("should filter to only unverified providers when verified=false", async () => {
			const mockListings = [
				{
					id: "verified_provider",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 20,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "unverified_provider",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					distance: 10,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				}
			];

			let capturedWhere;
			(mockPrisma.listing.findMany as jest.Mock).mockImplementation((args) => {
				capturedWhere = args.where;
				// Filter to simulate database behavior
				const filtered = mockListings.filter(
					(listing) =>
						listing.rv_help_verification_level === RVHelpVerificationLevel.NONE
				);
				return Promise.resolve(filtered);
			});

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-repair",
					filters: {
						verified: false
					}
				},
				1,
				10
			);

			// Should only include unverified providers (NONE level)
			expect(result.listings.length).toBe(1);
			expect(result.listings.map((l) => l.id)).toContain("unverified_provider");
			expect(result.listings.map((l) => l.id)).not.toContain(
				"verified_provider"
			);

			// Check that the WHERE clause has the correct verification filter within the AND clause
			expect(capturedWhere.AND[0]).toMatchObject({
				rv_help_verification_level: RVHelpVerificationLevel.NONE
			});
		});

		it("should include all providers when verified=undefined", async () => {
			const mockListings = [
				{
					id: "verified_provider",
					rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
					distance: 20,
					location: { latitude: 35.1, longitude: -85.1, radius: 100 }
				},
				{
					id: "unverified_provider",
					rv_help_verification_level: RVHelpVerificationLevel.NONE,
					distance: 10,
					location: { latitude: 35.2, longitude: -85.2, radius: 100 }
				}
			];

			let capturedWhere;
			(mockPrisma.listing.findMany as jest.Mock).mockImplementation((args) => {
				capturedWhere = args.where;
				return Promise.resolve(mockListings);
			});

			const result = await SearchService.getListingsByLatLong(
				{
					...baseParams,
					category: "rv-repair",
					filters: {
						verified: undefined
					}
				},
				1,
				10
			);

			// Should include all providers
			expect(result.listings.length).toBe(2);
			expect(result.listings.map((l) => l.id)).toContain("verified_provider");
			expect(result.listings.map((l) => l.id)).toContain("unverified_provider");

			// Check that no verification filter is applied to WHERE clause (should not have rv_help_verification_level)
			expect(capturedWhere.AND[0].rv_help_verification_level).toBeUndefined();
		});
	});

	describe("findNearestCitiesWithProviders", () => {
		const mockListings = [
			{
				id: "listing1",
				locations: [{
					city: "Nashville",
					state: "TN",
					latitude: 36.1627,
					longitude: -86.7816,
					radius: 50,
					default: true,
				}]
			},
			{
				id: "listing2",
				locations: [{
					city: "Nashville",
					state: "TN",
					latitude: 36.1627,
					longitude: -86.7816,
					radius: 75,
					default: true,
				}]
			},
			{
				id: "listing3",
				locations: [{
					city: "Memphis",
					state: "TN",
					latitude: 35.1495,
					longitude: -90.049,
					radius: 100,
					default: true,
				}]
			}
		];

		beforeEach(() => {
			jest.clearAllMocks();
		});

		it("should return nearest cities with accurate provider counts", async () => {
			(mockPrisma.listing.findMany as jest.Mock)
				.mockResolvedValueOnce(mockListings) // First call for finding cities
				.mockResolvedValueOnce([mockListings[0], mockListings[1]]) // Nashville providers
				.mockResolvedValueOnce([mockListings[2]]); // Memphis providers

			const result = await SearchService.findNearestCitiesWithProviders(
				36.1627,
				-86.7816,
				"rv-repair"
			);

			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({
				city: "Nashville",
				state: "TN",
				distance: 0,
				techCount: 2
			});
			expect(result[1]).toEqual({
				city: "Memphis",
				state: "TN",
				distance: expect.any(Number),
				techCount: 1
			});

			// Verify the initial query
			expect(mockPrisma.listing.findMany).toHaveBeenNthCalledWith(1, {
				where: {
					AND: [
						{
							is_active: true,
							categories: {
								path: ["rv-repair", "selected"],
								equals: true
							}
						},
						expect.any(Object) // For the location bounding box object
					]
				},
				select: {
					id: true,
					locations: {
						select: {
							city: true,
							state: true,
							latitude: true,
							longitude: true,
							radius: true,
							default: true,
							start_date: true,
							end_date: true,
						}
					}
				}
			});
		});

		it("should handle empty results", async () => {
			(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([]);

			const result = await SearchService.findNearestCitiesWithProviders(
				36.1627,
				-86.7816,
				"rv-repair"
			);

			expect(result).toHaveLength(0);
		});

		it("should handle listings without location data", async () => {
			const listingsWithNullLocation = [
				{ id: "listing1", location: null },
				...mockListings
			];

			(mockPrisma.listing.findMany as jest.Mock)
				.mockResolvedValueOnce(listingsWithNullLocation)
				.mockResolvedValueOnce([mockListings[0], mockListings[1]])
				.mockResolvedValueOnce([mockListings[2]]);

			const result = await SearchService.findNearestCitiesWithProviders(
				36.1627,
				-86.7816,
				"rv-repair"
			);

			expect(result.length).toBeGreaterThan(0);
			expect(result[0].techCount).toBe(2);
		});
	});

	it("should apply radius parameter filter before service radius filter", async () => {
		const mockListings = [
			{
				id: "within_both_radii",
				rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
				distance: 20,
				location: {
					latitude: 35.1,
					longitude: -85.1,
					radius: 50
				}
			},
			{
				id: "within_service_radius_but_outside_search_radius",
				rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
				distance: 30,
				location: {
					latitude: 35.2,
					longitude: -85.2,
					radius: 50
				}
			},
			{
				id: "within_search_radius_but_outside_service_radius",
				rv_help_verification_level: RVHelpVerificationLevel.VERIFIED,
				distance: 15,
				location: {
					latitude: 35.3,
					longitude: -85.3,
					radius: 10
				}
			}
		];

		(mockPrisma.listing.findMany as jest.Mock).mockResolvedValue(mockListings);
		(mockPrisma.listing.count as jest.Mock).mockResolvedValue(mockListings.length);

		const result = await SearchService.getListingsByLatLong(
			{
				...baseParams,
				category: "rv-inspection",
				radius: "25" // 25 mile search radius
			},
			1,
			10
		);

		// Should only include listings within both the search radius (25 miles) and service radius
		expect(result.listings.map((l) => l.id)).toEqual(["within_both_radii"]);
		expect(result.listings.length).toBe(1);
	});

	describe("vacation mode filtering", () => {
		it("should include vacation mode filtering in where clause", () => {
			const params = {
				category: "rv-repair",
				filters: {}
			};

			const whereClause = SearchService.buildWhereClause(params);

			// Should include vacation mode filtering
			expect(whereClause).toHaveProperty("OR");
			expect(whereClause.OR).toEqual([
				{ vacation_mode: null },
				{ vacation_mode: { path: ["enabled"], equals: false } },
				{ vacation_mode: { path: ["enabled"], not: true } }
			]);
		});

		it("should include vacation mode filtering with other filters", () => {
			const params = {
				category: "rv-repair",
				filters: {
					verified: true,
					showDiscountProviders: true
				}
			};

			const whereClause = SearchService.buildWhereClause(params);

			// Should include vacation mode filtering along with other filters
			expect(whereClause).toHaveProperty("OR");
			expect(whereClause.OR).toEqual([
				{ vacation_mode: null },
				{ vacation_mode: { path: ["enabled"], equals: false } },
				{ vacation_mode: { path: ["enabled"], not: true } }
			]);
			expect(whereClause.rv_help_verification_level).toEqual({
				not: RVHelpVerificationLevel.NONE
			});
		});

		it("should handle vacation mode filtering for rv-inspection category", () => {
			const params = {
				category: "rv-inspection",
				filters: {}
			};

			const whereClause = SearchService.buildWhereClause(params);

			// Should include vacation mode filtering
			expect(whereClause).toHaveProperty("OR");
			expect(whereClause.OR).toEqual([
				{ vacation_mode: null },
				{ vacation_mode: { path: ["enabled"], equals: false } },
				{ vacation_mode: { path: ["enabled"], not: true } }
			]);
		});
	});
});

const createMockDate = (fixedDate: string) => {
	return class extends Date {
		constructor(...args: any[]) {
			if (args.length) {
				super(...args);
			} else {
				super(fixedDate);
			}
		}

		static now() {
			return new Date(fixedDate).getTime();
		}
	} as DateConstructor;
};

// Then in the test:
global.Date = createMockDate("2024-01-01");
// and later
global.Date = createMockDate("2024-01-02");
