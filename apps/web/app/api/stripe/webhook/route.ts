import prisma from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { stripe } from "@/lib/stripe";
import { InvoiceStatus, TimelineEventType } from "@rvhelp/database";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import Strip<PERSON> from "stripe";


const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: Request) {
	const body = await request.text();
	const headersList = headers();
	const signature = headersList.get("stripe-signature");

	if (!signature || !webhookSecret) {
		adminLogger.log("Missing signature or webhook secret", {
			"signature": signature,
			"webhookSecret": webhookSecret
		}, "error");
		return NextResponse.json(
			{ error: "Missing signature or webhook secret" },
			{ status: 400 }
		);
	}

	let event;

	try {
		event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
	} catch (err) {
		console.error(`⚠️  Webhook signature verification failed.`, err);
		return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
	}

	try {
		switch (event.type) {
			case "payment_intent.succeeded": {
				const paymentIntent = event.data.object as Stripe.PaymentIntent;

				// Handle invoice payments (from both direct payment intents and checkout sessions)
				if (paymentIntent.metadata?.type === "invoice_payment") {
					await handleProviderInvoicePayment(paymentIntent);
				}

				// Handle service request payments
				if (paymentIntent.metadata?.service_request_id && paymentIntent.metadata?.transaction_id) {
					await handleServiceRequestPayment(paymentIntent);
				}

				break;
			}

			case "checkout.session.completed": {
				const session = event.data.object as Stripe.Checkout.Session;

				// Log general checkout completions
				console.log(`Checkout session ${session.id} completed`);
				break;
			}

			case "payment_intent.payment_failed": {
				const paymentIntent = event.data.object as Stripe.PaymentIntent;

				// Handle failed service request payments
				if (paymentIntent.metadata?.transaction_id) {
					await handleFailedPayment(paymentIntent);
				}

				break;
			}

			case "charge.refunded": {
				const charge = event.data.object as Stripe.Charge;

				// Handle refunds for service requests
				if (charge.metadata?.transaction_id) {
					await handleRefund(charge);
				}

				break;
			}

			case "customer.subscription.updated":
			case "customer.subscription.deleted": {
				const subscription = event.data.object as Stripe.Subscription;
				console.log(`Subscription ${subscription.id} ${event.type}`);
				break;
			}

			default: {
				console.log(`Unhandled event type ${event.type}`);
			}
		}

		return NextResponse.json({ received: true });
	} catch (error) {
		console.error("Error processing webhook:", error);
		return NextResponse.json(
			{ error: "Webhook handler failed" },
			{ status: 500 }
		);
	}
}

// Handle invoice payment via payment intent
async function handleProviderInvoicePayment(paymentIntent: Stripe.PaymentIntent) {
	const invoiceId = paymentIntent.metadata.provider_invoice_id;

	if (!invoiceId) {
		console.error("Provider invoice ID not found in payment intent metadata");
		return;
	}

	const invoice = await prisma.invoice.findUnique({
		where: { id: invoiceId },
		select: {
			status: true,
			payment_intent_id: true,
			payment_transfer_id: true
		}
	});

	if (!invoice) {
		console.error(`Invoice ${invoiceId} not found`);
		return;
	}

	// Check if invoice is already paid
	if (invoice.status === "PAID") {
		console.log(`Invoice ${invoiceId} is already paid, skipping webhook processing`);
		return;
	}

	// Check if this payment intent has already been processed
	if (invoice.payment_intent_id && invoice.payment_intent_id === paymentIntent.id) {
		console.log(`Payment intent ${paymentIntent.id} already processed for invoice ${invoiceId}`);
		return;
	}

	// Update invoice status to PAID
	await prisma.invoice.update({
		where: { id: invoiceId },
		data: {
			status: InvoiceStatus.PAID,
			payment_intent_id: paymentIntent.id,
			payment_transfer_id: paymentIntent.id,
			paid_at: new Date()
		}
	});

	// get the warranty request id from the invoice
	const warrantyRequest = await prisma.warrantyRequest.findUnique({
		where: { provider_invoice_id: invoiceId }
	});

	console.log(`Invoice ${invoiceId} marked as paid via payment intent`);

	// Update warranty request status if this is a warranty invoice
	if (warrantyRequest) {
		await prisma.warrantyRequest.update({
			where: { id: warrantyRequest.id },
			data: { status: "INVOICE_PAID" }
		});

		// Create timeline update
		await prisma.timelineUpdate.create({
			data: {
				warranty_request_id: warrantyRequest.id,
				event_type: "INVOICE_PAID" as TimelineEventType,
				details: { notes: "Payment completed via Stripe (webhook)" },
				date: new Date(),
				updated_by_id: null // System update, no user ID
			}
		});

		console.log(`Warranty request ${warrantyRequest.id} status updated to INVOICE_PAID via webhook`);
	}
}


// Handle service request payment
async function handleServiceRequestPayment(paymentIntent: Stripe.PaymentIntent) {
	const { service_request_id, transaction_id } = paymentIntent.metadata;

	// Update transaction status
	await prisma.transaction.update({
		where: { id: transaction_id },
		data: {
			status: "completed",
			stripe_payment_id: paymentIntent.id,
			payment_method_type: paymentIntent.payment_method_types[0],
			payment_method_last4: (paymentIntent.payment_method as any)?.card?.last4,
			payment_method_brand: (paymentIntent.payment_method as any)?.card?.brand,
			payment_method_expiry: paymentIntent.payment_method
				? `${(paymentIntent.payment_method as any)?.card?.exp_month}/${(paymentIntent.payment_method as any)?.card?.exp_year}`
				: null
		}
	});

	// Get the transaction to check if this is a premium membership payment
	const transaction = await prisma.transaction.findUnique({
		where: { id: transaction_id },
		select: {
			payment_method: true,
			user_id: true,
			amount_in_cents: true
		}
	});

	if (transaction?.payment_method === "rv_help_premium") {
		await prisma.membership.upsert({
			where: { user_id: transaction.user_id },
			create: {
				user_id: transaction.user_id,
				level: "PREMIUM",
				is_active: true,
				stripe_subscription_id: paymentIntent.id,
				amount_paid: transaction.amount_in_cents
			},
			update: {
				level: "PREMIUM",
				is_active: true,
				stripe_subscription_id: paymentIntent.id,
				amount_paid: transaction.amount_in_cents
			}
		});

		console.log(`Premium membership created/updated for user ${transaction.user_id}`);
	}

	console.log(`Service request ${service_request_id} payment completed via webhook`);
}

// Handle failed payment
async function handleFailedPayment(paymentIntent: Stripe.PaymentIntent) {
	const { transaction_id } = paymentIntent.metadata;

	await prisma.transaction.update({
		where: { id: transaction_id },
		data: {
			status: "failed",
			error_message: paymentIntent.last_payment_error?.message
		}
	});

	console.log(`Transaction ${transaction_id} marked as failed`);
}

// Handle refunds
async function handleRefund(charge: Stripe.Charge) {
	const { transaction_id } = charge.metadata;

	await prisma.transaction.update({
		where: { id: transaction_id },
		data: {
			status: "refunded",
			refunded_at: new Date(),
			refund_amount: charge.amount_refunded / 100,
			refund_amount_cents: charge.amount_refunded,
			refund_reason: "Customer request"
		}
	});

	console.log(`Transaction ${transaction_id} refunded`);
}

export const runtime = "nodejs";
export const dynamic = "force-dynamic";
